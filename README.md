# RokitBox - Evidence hardwaru

Webová aplikace pro evidenci hardwaru organizací s rolemi a správou uživatelů.

## Funkce

- **Autentifikace** - Přih<PERSON>š<PERSON><PERSON><PERSON> uživatelů (bez registrace)
- **Role uživatelů** - Superadmin, Admin organizace, Uživatel
- **Správa organizací** - Vytváření a správa organizací (pouze superadmin)
- **Spr<PERSON>va uživatelů** - Vyt<PERSON><PERSON><PERSON><PERSON><PERSON> (superadmin pro všechny, admin pro svou organizaci)
- **Evidence zařízení** - CRUD operace pro hardware s filtrováním podle organizace
- **Dashboard** - Přehledy a statistiky

## Technologie

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Prisma ORM + SQLite
- **Auth**: NextAuth.js
- **UI**: Lucide React icons

## Nastavení

### 1. Instalace závislostí

```bash
npm install
```

### 2. Databáze

Vygenerujte Prisma klienta a vytvořte databázi:

```bash
npx prisma generate
npx prisma db push
```

### 3. Seed data

Vytvořte prvního superadmina:

```bash
npm run db:seed
```

Tím se vytvoří:
- **Email**: <EMAIL>
- **Heslo**: admin123
- **Role**: Superadmin

### 4. Spuštění

```bash
npm run dev
```

Aplikace bude dostupná na `http://localhost:3000`

## Struktura rolí

### Superadmin
- Přístup ke všem organizacím
- Správa organizací (vytváření, editace, mazání)
- Správa všech uživatelů
- Přístup ke všem zařízením

### Admin organizace
- Přístup pouze ke své organizaci
- Správa uživatelů ve své organizaci
- Správa zařízení ve své organizaci

### Uživatel
- Přístup pouze ke své organizaci
- Prohlížení a editace zařízení ve své organizaci

## Databázová struktura

### Organization
- Organizace v systému
- Vztah 1:N s uživateli a zařízeními

### User
- Uživatelé s rolemi (SUPERADMIN, ADMIN, USER)
- NextAuth.js kompatibilní struktura
- Vztah N:1 s organizací

### Device
- Evidence hardwaru
- Filtrování podle organizace
- Sledování stavu, záruky, umístění

### Account, Session, VerificationToken
- NextAuth.js tabulky pro autentifikaci

## Bezpečnost

- NextAuth.js pro autentifikaci
- Middleware pro ochranu routes
- Role-based access control v API routes
- Automatické filtrování dat podle organizace a rolí

## Poznámky

- Registrace je zakázána - účty vytváří pouze superadmin/admin
- Design vychází z loga RokIT s oranžovo-černou barevnou schémou
- Responzivní design pro desktop i mobil
- Lokální SQLite databáze - žádné externí závislosti
