{"name": "rokit-box", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint", "test": "echo \"No tests specified\" && exit 0", "db:seed": "tsx prisma/seed.ts", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "production:setup": "npm ci --only=production && npm run db:generate && npm run db:migrate && npm run build", "production:start": "NODE_ENV=production npm start", "create-superadmin": "node scripts/create-superadmin.js", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "generate-pdf": "node scripts/generate-pdf.js", "docs:install": "npm install puppeteer --save-dev", "docs:build": "npm run docs:install && npm run generate-pdf"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.15.0", "@types/nodemailer": "^7.0.1", "bcryptjs": "^3.0.2", "critters": "^0.0.23", "lucide-react": "^0.542.0", "mailparser": "^3.7.4", "next": "15.5.2", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2", "smtp-server": "^3.14.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "puppeteer": "^24.19.0", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}