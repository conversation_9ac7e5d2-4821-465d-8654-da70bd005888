<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RokitBox - Instalační příručka</title>
    <style>
        @page {
            margin: 2cm;
            size: A4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #f97316;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #f97316;
            font-size: 2.5em;
            margin: 0;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 1.1em;
            margin-top: 10px;
        }
        
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        
        .toc h2 {
            margin-top: 0;
            color: #f97316;
        }
        
        .toc ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        h1, h2, h3 {
            color: #f97316;
            page-break-after: avoid;
        }
        
        h1 {
            font-size: 2em;
            border-bottom: 2px solid #f97316;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        
        h2 {
            font-size: 1.5em;
            margin-top: 30px;
        }
        
        h3 {
            font-size: 1.2em;
            margin-top: 25px;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #f97316;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .step h3 {
            margin-top: 0;
            color: #f97316;
        }
        
        .requirements {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #28a745;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #dc3545;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }
        
        .command {
            background: #1a202c;
            color: #68d391;
            padding: 10px 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border-left: 4px solid #68d391;
        }
        
        .checklist {
            list-style: none;
            padding-left: 0;
        }
        
        .checklist li {
            margin: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .checklist li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #f97316;
            color: white;
        }
        
        .url {
            color: #f97316;
            text-decoration: none;
            word-break: break-all;
        }
        
        @media print {
            body {
                font-size: 12pt;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            h1, h2, h3 {
                page-break-after: avoid;
            }
            
            .step, .requirements, .warning, .success, .error {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📦 RokitBox</h1>
        <div class="subtitle">
            <strong>Kompletní instalační příručka</strong><br>
            Verze 1.0 | Prosinec 2024<br>
            <a href="https://github.com/jansloupensky/Rokit_Box" class="url">https://github.com/jansloupensky/Rokit_Box</a>
        </div>
    </div>

    <div class="toc">
        <h2>📋 Obsah</h2>
        <ol>
            <li><a href="#requirements">Požadavky na systém</a></li>
            <li><a href="#server-setup">Příprava serveru</a></li>
            <li><a href="#installation">Instalace aplikace</a></li>
            <li><a href="#configuration">Konfigurace</a></li>
            <li><a href="#database">Databáze</a></li>
            <li><a href="#deployment">Build a spuštění</a></li>
            <li><a href="#verification">Ověření funkčnosti</a></li>
            <li><a href="#monitoring">Monitoring a údržba</a></li>
            <li><a href="#troubleshooting">Řešení problémů</a></li>
        </ol>
    </div>

    <div class="page-break"></div>

    <h1 id="requirements">🖥️ 1. Požadavky na systém</h1>

    <div class="requirements">
        <h3>Minimální požadavky:</h3>
        <ul class="checklist">
            <li><strong>OS:</strong> Ubuntu 20.04 LTS nebo novější</li>
            <li><strong>RAM:</strong> 2 GB (doporučeno 4 GB)</li>
            <li><strong>Disk:</strong> 20 GB volného místa</li>
            <li><strong>CPU:</strong> 2 cores (doporučeno 4 cores)</li>
            <li><strong>Síť:</strong> Veřejná IP adresa</li>
            <li><strong>Doména:</strong> Registrovaná doména (např. rokitbox.cz)</li>
        </ul>
    </div>

    <div class="requirements">
        <h3>Software požadavky:</h3>
        <ul class="checklist">
            <li>Node.js 18+</li>
            <li>PostgreSQL 13+</li>
            <li>Nginx</li>
            <li>SSL certifikát (Let's Encrypt)</li>
        </ul>
    </div>

    <h1 id="server-setup">🚀 2. Příprava serveru</h1>

    <div class="step">
        <h3>2.1 Připojení na server</h3>
        <div class="command">ssh root@your-server-ip</div>
    </div>

    <div class="step">
        <h3>2.2 Aktualizace systému</h3>
        <div class="command">apt update && apt upgrade -y</div>
    </div>

    <div class="step">
        <h3>2.3 Automatická instalace prostředí</h3>
        <pre><code># Stáhněte setup script
wget https://raw.githubusercontent.com/jansloupensky/Rokit_Box/main/scripts/setup-production.sh

# Nastavte oprávnění
chmod +x setup-production.sh

# Spusťte instalaci
./setup-production.sh</code></pre>
        
        <div class="success">
            <strong>Co script nainstaluje:</strong>
            <ul class="checklist">
                <li>Node.js 18 a npm</li>
                <li>PostgreSQL databázi</li>
                <li>Nginx web server</li>
                <li>PM2 process manager</li>
                <li>SSL certifikát (Let's Encrypt)</li>
                <li>Firewall (UFW)</li>
                <li>Fail2ban bezpečnost</li>
                <li>Monitoring nástroje</li>
            </ul>
        </div>
    </div>

    <div class="step">
        <h3>2.4 Konfigurace DNS</h3>
        <p>Nastavte DNS záznamy pro vaši doménu:</p>
        <pre><code>A     @           YOUR_SERVER_IP
A     www         YOUR_SERVER_IP
CNAME mail        YOUR_SERVER_IP</code></pre>
    </div>

    <div class="page-break"></div>

    <h1 id="installation">📥 3. Instalace aplikace</h1>

    <div class="step">
        <h3>3.1 Klonování repository</h3>
        <pre><code>cd /var/www
git clone https://github.com/jansloupensky/Rokit_Box.git rokitbox
cd rokitbox</code></pre>
    </div>

    <div class="step">
        <h3>3.2 Instalace závislostí</h3>
        <div class="command">npm ci --only=production</div>
    </div>

    <div class="step">
        <h3>3.3 Generování Prisma klienta</h3>
        <div class="command">npx prisma generate</div>
    </div>

    <h1 id="configuration">⚙️ 4. Konfigurace</h1>

    <div class="step">
        <h3>4.1 Environment variables</h3>
        <pre><code># Zkopírujte template
cp .env.production.template .env.production

# Upravte konfiguraci
nano .env.production</code></pre>
    </div>

    <div class="step">
        <h3>4.2 Vyplňte následující hodnoty:</h3>
        <pre><code># === DATABASE ===
DATABASE_URL="postgresql://rokitbox_user:YOUR_DB_PASSWORD@localhost:5432/rokitbox_prod"

# === NEXTAUTH ===
NEXTAUTH_SECRET="your-super-secure-32-character-secret"
NEXTAUTH_URL="https://your-domain.com"

# === SMTP EMAIL ===
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-gmail-app-password"
SMTP_FROM="RokitBox &lt;<EMAIL>&gt;"

# === PRODUCTION ===
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://your-domain.com"</code></pre>
    </div>

    <div class="step">
        <h3>4.3 Generování NEXTAUTH_SECRET</h3>
        <div class="command">openssl rand -base64 32</div>
    </div>

    <div class="warning">
        <h3>Gmail SMTP setup</h3>
        <ol>
            <li>Zapněte 2FA na Gmail účtu</li>
            <li>Vygenerujte App Password:
                <ul>
                    <li>Google Account → Security → 2-Step Verification → App passwords</li>
                    <li>Vyberte "Mail" a "Other (Custom name)"</li>
                    <li>Použijte vygenerované heslo v <code>SMTP_PASSWORD</code></li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="page-break"></div>

    <h1 id="database">🗄️ 5. Databáze</h1>

    <div class="step">
        <h3>5.1 Spuštění migrací</h3>
        <div class="command">npx prisma migrate deploy</div>
    </div>

    <div class="step">
        <h3>5.2 Vytvoření prvního superadmin uživatele</h3>
        <div class="command">npm run create-superadmin</div>
        <p>Postupujte podle instrukcí a zadejte:</p>
        <ul>
            <li>Email adresu</li>
            <li>Heslo (min. 8 znaků)</li>
        </ul>
    </div>

    <h1 id="deployment">🏗️ 6. Build a spuštění</h1>

    <div class="step">
        <h3>6.1 Build aplikace</h3>
        <div class="command">npm run build</div>
    </div>

    <div class="step">
        <h3>6.2 Spuštění s PM2</h3>
        <pre><code>pm2 start ecosystem.config.js
pm2 save
pm2 startup</code></pre>
    </div>

    <div class="step">
        <h3>6.3 Konfigurace Nginx</h3>
        <pre><code># Zkopírujte konfiguraci
cp nginx.conf /etc/nginx/sites-available/your-domain.com

# Upravte doménu v souboru
sed -i 's/rokitbox.cz/your-domain.com/g' /etc/nginx/sites-available/your-domain.com

# Aktivujte site
ln -sf /etc/nginx/sites-available/your-domain.com /etc/nginx/sites-enabled/

# Testujte konfiguraci
nginx -t

# Restartujte Nginx
systemctl restart nginx</code></pre>
    </div>

    <div class="page-break"></div>

    <h1 id="verification">✅ 7. Ověření funkčnosti</h1>

    <div class="step">
        <h3>7.1 Health check</h3>
        <div class="command">curl https://your-domain.com/api/health</div>
        
        <p><strong>Očekávaný výstup:</strong></p>
        <pre><code>{
  "status": "healthy",
  "timestamp": "2024-12-05T10:30:00.000Z",
  "version": "1.0.0",
  "checks": {
    "database": {
      "status": "healthy",
      "responseTime": "15ms"
    }
  }
}</code></pre>
    </div>

    <div class="step">
        <h3>7.2 Test přihlášení</h3>
        <ol>
            <li>Otevřete <code>https://your-domain.com/login</code></li>
            <li>Přihlaste se s vytvořeným superadmin účtem</li>
            <li>Ověřte přístup k admin panelu na <code>/admin</code></li>
        </ol>
    </div>

    <div class="step">
        <h3>7.3 Test email funkčnosti</h3>
        <ol>
            <li>Vytvořte nového uživatele v admin panelu</li>
            <li>Zkontrolujte, že email byl odeslán</li>
            <li>V development módu zkontrolujte <code>/dev/emails</code></li>
        </ol>
    </div>

    <h1 id="monitoring">📊 8. Monitoring a údržba</h1>

    <div class="step">
        <h3>8.1 Užitečné příkazy</h3>
        <table>
            <tr>
                <th>Příkaz</th>
                <th>Popis</th>
            </tr>
            <tr>
                <td><code>pm2 status</code></td>
                <td>Status aplikace</td>
            </tr>
            <tr>
                <td><code>pm2 logs rokitbox</code></td>
                <td>Zobrazení logů</td>
            </tr>
            <tr>
                <td><code>pm2 restart rokitbox</code></td>
                <td>Restart aplikace</td>
            </tr>
            <tr>
                <td><code>pm2 monit</code></td>
                <td>Monitoring</td>
            </tr>
            <tr>
                <td><code>systemctl status nginx</code></td>
                <td>Nginx status</td>
            </tr>
        </table>
    </div>

    <div class="success">
        <h3>8.2 Automatické úlohy</h3>
        <p>Setup script automaticky nakonfiguruje:</p>
        <ul class="checklist">
            <li>Health check každých 5 minut</li>
            <li>SSL renewal automaticky</li>
            <li>Log rotation denně</li>
            <li>Database backup denně</li>
        </ul>
    </div>

    <div class="page-break"></div>

    <h1 id="troubleshooting">🚨 9. Řešení problémů</h1>

    <div class="error">
        <h3>Aplikace se nespustí</h3>
        <pre><code># Zkontrolujte logy
pm2 logs rokitbox

# Zkontrolujte konfiguraci
cat .env.production

# Restartujte aplikaci
pm2 restart rokitbox</code></pre>
    </div>

    <div class="error">
        <h3>Databáze nefunguje</h3>
        <pre><code># Zkontrolujte PostgreSQL
systemctl status postgresql

# Testujte připojení
psql -h localhost -U rokitbox_user -d rokitbox_prod

# Zkontrolujte migrace
npx prisma migrate status</code></pre>
    </div>

    <div class="error">
        <h3>SSL problémy</h3>
        <pre><code># Obnovte certifikát
certbot renew

# Zkontrolujte konfiguraci
nginx -t

# Restartujte Nginx
systemctl restart nginx</code></pre>
    </div>

    <div class="warning">
        <h3>Bezpečnostní doporučení</h3>
        <ul class="checklist">
            <li>Pravidelně aktualizujte systém</li>
            <li>Monitorujte logy pro podezřelou aktivitu</li>
            <li>Zálohujte databázi pravidelně</li>
            <li>Používejte silná hesla</li>
            <li>Omezte SSH přístup</li>
        </ul>
    </div>

    <div class="footer">
        <p><strong>📞 Podpora</strong></p>
        <p>
            <strong>GitHub Issues:</strong> <a href="https://github.com/jansloupensky/Rokit_Box/issues" class="url">https://github.com/jansloupensky/Rokit_Box/issues</a><br>
            <strong>Email:</strong> <EMAIL><br>
            <strong>Health Check:</strong> https://your-domain.com/api/health
        </p>
        <hr>
        <p>© 2024 RokitBox - Všechna práva vyhrazena</p>
    </div>
</body>
</html>
