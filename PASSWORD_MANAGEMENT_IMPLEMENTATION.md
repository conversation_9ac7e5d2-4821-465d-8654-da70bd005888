# Password Management + Local Email Server - RokitBox

Tato dokumentace popisuje implementaci nového systému spr<PERSON>vy hesel s lokálním email serverem, kde admin zadáv<PERSON> pouze email a organizaci, a uživatel dostane email s odkazem na nastavení hesla.

## Nová funkcionalita

### 🔧 Změny v logice přihlašování

#### Před změnou:
- Admin musel zadat email, heslo, roli a organizaci
- Heslo bylo povinné při vytváření uživatele
- Uživatel dostal přihlašovací údaje přímo

#### Po změně:
- Admin zadává pouze email, roli a organizaci
- Heslo se nenastavuje při vytváření
- Uživatel dostane email s odkazem na nastavení hesla
- Přidána možnost reset hesla

## Implementované komponenty

### 1. Databázové změny (`prisma/schema.prisma`)

```prisma
model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expires   DateTime
  used      <PERSON>olean  @default(false)
  createdAt DateTime @default(now())
}
```

### 2. Email služba (`src/lib/email.ts`)

#### Konfigurace SMTP
```typescript
const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST || 'localhost',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
})
```

#### Email šablony
- **Set Password Email**: Pro nové uživatele (24h platnost)
- **Reset Password Email**: Pro reset hesla (1h platnost)

### 3. Token management (`src/lib/tokens.ts`)

#### Funkce pro práci s tokeny
- `generateSecureToken()` - Generování bezpečného tokenu
- `createPasswordResetToken()` - Vytvoření reset tokenu
- `createSetPasswordToken()` - Vytvoření setup tokenu
- `validatePasswordResetToken()` - Validace tokenu
- `markTokenAsUsed()` - Označení tokenu jako použitého
- `cleanupExpiredTokens()` - Úklid vypršených tokenů

### 4. API endpointy

#### `/api/users` (POST) - Upraveno
```typescript
// Před: vyžadoval email + password
{ email, password, role, organizationId }

// Po: vyžaduje pouze email
{ email, role, organizationId }
```

#### `/api/auth/set-password` - Nový
- **POST**: Nastavení hesla pomocí tokenu
- **GET**: Validace tokenu a získání informací

#### `/api/auth/reset-password` - Nový
- **POST**: Žádost o reset hesla (odeslání emailu)

### 5. Nové stránky

#### `/set-password` - Nastavení hesla
- Validace tokenu
- Formulář pro nastavení hesla
- Potvrzení hesla
- Přesměrování na login

#### `/reset-password` - Reset hesla
- Validace reset tokenu
- Formulář pro nové heslo
- Potvrzení hesla

#### `/forgot-password` - Zapomenuté heslo
- Formulář pro zadání emailu
- Odeslání reset odkazu
- Potvrzovací zpráva

### 6. Aktualizované komponenty

#### Login stránka (`/login`)
- Přidán odkaz "Zapomněli jste heslo?"
- Přesměrování na `/forgot-password`

#### Users management (`/users`)
- Odstraněno pole pro heslo
- Přidána informační zpráva o emailu
- Upravená logika vytváření uživatelů

#### Middleware (`src/middleware.ts`)
- Povolený přístup k password stránkám bez autentifikace
- Aktualizované route patterns

## Workflow

### 1. Vytvoření nového uživatele

```mermaid
sequenceDiagram
    participant A as Admin
    participant S as Server
    participant D as Database
    participant E as Email Service
    participant U as User

    A->>S: POST /api/users {email, role, org}
    S->>D: Create user (password: null)
    S->>D: Create password token (24h)
    S->>E: Send setup email
    E->>U: Email with setup link
    U->>S: GET /set-password?token=xxx
    S->>D: Validate token
    U->>S: POST /api/auth/set-password
    S->>D: Update user password
    S->>D: Mark token as used
```

### 2. Reset hesla

```mermaid
sequenceDiagram
    participant U as User
    participant S as Server
    participant D as Database
    participant E as Email Service

    U->>S: POST /api/auth/reset-password {email}
    S->>D: Create reset token (1h)
    S->>E: Send reset email
    E->>U: Email with reset link
    U->>S: GET /reset-password?token=xxx
    S->>D: Validate token
    U->>S: POST /api/auth/set-password
    S->>D: Update user password
    S->>D: Mark token as used
```

## Konfigurace

### Environment variables (.env.local)

```bash
# Email Configuration - Local Development
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_SECURE="false"
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_FROM="<EMAIL>"
```

### Doporučené SMTP služby

#### Pro development:
- **MailHog**: Lokální SMTP server pro testování
- **Mailtrap**: Online služba pro testování emailů

#### Pro production:
- **Gmail SMTP**: smtp.gmail.com:587
- **Outlook SMTP**: smtp-mail.outlook.com:587
- **SendGrid**: Profesionální email služba
- **Amazon SES**: AWS email služba

## Bezpečnostní opatření

### 1. Token security
- **Kryptograficky bezpečné tokeny**: 32 bytů hex
- **Časové omezení**: 24h pro setup, 1h pro reset
- **Jednorázové použití**: Token se označí jako použitý
- **Automatický úklid**: Vypršené tokeny se mažou

### 2. Email security
- **Neodhalování existence účtu**: Stejná odpověď pro existující i neexistující email
- **Rate limiting**: Doporučeno implementovat v produkci
- **HTTPS odkazy**: Všechny odkazy v emailech používají HTTPS

### 3. Password policy
- **Minimální délka**: 6 znaků
- **Potvrzení hesla**: Kontrola shody hesel
- **Bcrypt hashing**: Bezpečné hashování hesel

## Lokální Email Server

### 🚀 Automatický lokální email server

Aplikace má vestavěný lokální email server pro development:

- **Automatické spuštění**: Spustí se s aplikací
- **Ukládání emailů**: Emaily se ukládají do `.emails/` složky jako JSON soubory
- **Web viewer**: Prohlížení emailů na `http://localhost:3000/dev/emails`
- **Žádná konfigurace**: Funguje okamžitě bez nastavení

### 📧 Jak to funguje

1. **Development režim**: Emaily se ukládají do souborů místo odesílání
2. **Production režim**: Používá skutečný SMTP server
3. **Email viewer**: Webové rozhraní pro prohlížení uložených emailů
4. **Auto-refresh**: Automatické obnovování seznamu emailů

### 🔧 Testování

### 🧪 Test workflow

1. **Vytvoření uživatele**:
   - Přihlásit se jako admin
   - Jít na `/users`
   - Kliknout "Přidat uživatele"
   - Zadat email a organizaci
   - Email se automaticky uloží do `.emails/` složky

2. **Prohlížení emailu**:
   - Jít na `http://localhost:3000/dev/emails`
   - Najít email s nastavením hesla
   - Zkopírovat odkaz z emailu

3. **Nastavení hesla**:
   - Otevřít odkaz z emailu
   - Nastavit heslo
   - Přihlásit se s novým heslem

4. **Reset hesla**:
   - Na login stránce kliknout "Zapomněli jste heslo?"
   - Zadat email
   - Zkontrolovat `/dev/emails`
   - Kliknout na reset odkaz
   - Nastavit nové heslo

### 📁 Struktura souborů

```
.emails/
├── email_1703123456789_abc123.json
├── email_1703123567890_def456.json
└── ...
```

Každý email je uložen jako JSON soubor s:
- `id` - Unikátní identifikátor
- `from` - Odesílatel (<EMAIL>)
- `to` - Příjemce
- `subject` - Předmět
- `html` - HTML obsah
- `text` - Textový obsah
- `date` - Datum odeslání

## Výhody nového systému

### ✅ Pro administrátory
- **Jednodušší vytváření uživatelů**: Pouze email a organizace
- **Bezpečnější**: Admin nezná hesla uživatelů
- **Automatizované**: Email se odešle automaticky

### ✅ Pro uživatele
- **Vlastní heslo**: Uživatel si nastaví heslo sám
- **Reset možnost**: Možnost resetovat zapomenuté heslo
- **Bezpečné**: Heslo zná pouze uživatel

### ✅ Pro systém
- **Audit trail**: Sledování tokenů a jejich použití
- **Škálovatelné**: Automatické odesílání emailů
- **Bezpečné**: Kryptograficky bezpečné tokeny

## Budoucí vylepšení

### 🚀 Možná rozšíření
1. **Rate limiting**: Omezení počtu reset požadavků
2. **Email templates**: Pokročilejší HTML šablony
3. **Multi-language**: Podpora více jazyků
4. **SMS reset**: Alternativa k emailu
5. **2FA**: Dvoufaktorová autentifikace
6. **Password policy**: Pokročilejší pravidla pro hesla

## Závěr

Nový systém správy hesel poskytuje:
- ✅ **Bezpečnější workflow** pro vytváření uživatelů
- ✅ **Lepší uživatelskou zkušenost** s vlastními hesly
- ✅ **Automatizované procesy** s email notifikacemi
- ✅ **Moderní přístup** k password managementu
- ✅ **Škálovatelné řešení** pro větší organizace

Implementace je připravena pro produkční nasazení s odpovídající SMTP konfigurací.
