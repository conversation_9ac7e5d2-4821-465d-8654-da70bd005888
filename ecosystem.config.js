module.exports = {
  apps: [{
    name: 'rokitbox',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/rokitbox',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/rokitbox/error.log',
    out_file: '/var/log/rokitbox/out.log',
    log_file: '/var/log/rokitbox/combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000
  }]
}
