#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="rokitbox"
APP_DIR="/var/www/rokitbox"
BACKUP_DIR="/var/backups/rokitbox"
LOG_FILE="/var/log/rokitbox/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a $LOG_FILE
    exit 1
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        error "package.json not found. Are you in the right directory?"
    fi
    
    # Check if PM2 is installed
    if ! command -v pm2 &> /dev/null; then
        error "PM2 is not installed. Please install it first: npm install -g pm2"
    fi
    
    # Check if PostgreSQL is running
    if ! systemctl is-active --quiet postgresql; then
        error "PostgreSQL is not running. Please start it first."
    fi
    
    # Check disk space (minimum 1GB free)
    AVAILABLE_SPACE=$(df / | awk 'NR==2{print $4}')
    if [ $AVAILABLE_SPACE -lt 1048576 ]; then
        warning "Low disk space detected. Available: $(($AVAILABLE_SPACE/1024))MB"
    fi
    
    success "Pre-deployment checks passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    mkdir -p $BACKUP_DIR
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
    
    # Backup database
    log "Backing up database..."
    pg_dump -h localhost -U rokitbox_user rokitbox_prod > "$BACKUP_DIR/${BACKUP_NAME}_db.sql"
    
    # Backup application files (if exists)
    if [ -d "$APP_DIR" ]; then
        log "Backing up application files..."
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_app.tar.gz" -C "$APP_DIR" . --exclude=node_modules --exclude=.next
    fi
    
    # Keep only last 7 backups
    find $BACKUP_DIR -name "backup_*" -mtime +7 -delete
    
    success "Backup created: $BACKUP_NAME"
}

# Deploy application
deploy_application() {
    log "Starting deployment..."
    
    # Pull latest code
    log "Pulling latest code from repository..."
    git pull origin main || error "Failed to pull latest code"
    
    # Install dependencies
    log "Installing dependencies..."
    npm ci --only=production || error "Failed to install dependencies"
    
    # Generate Prisma client
    log "Generating Prisma client..."
    npx prisma generate || error "Failed to generate Prisma client"
    
    # Run database migrations
    log "Running database migrations..."
    npx prisma migrate deploy || error "Failed to run migrations"
    
    # Build application
    log "Building application..."
    npm run build || error "Failed to build application"
    
    success "Application deployed successfully"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Check if PM2 process exists
    if pm2 list | grep -q $APP_NAME; then
        log "Restarting PM2 process..."
        pm2 restart $APP_NAME || error "Failed to restart PM2 process"
    else
        log "Starting PM2 process..."
        pm2 start ecosystem.config.js || error "Failed to start PM2 process"
    fi
    
    # Save PM2 configuration
    pm2 save
    
    success "Services restarted successfully"
}

# Health check
health_check() {
    log "Running health check..."
    
    # Wait for application to start
    sleep 10
    
    # Check if application is responding
    HEALTH_URL="http://localhost:3000/api/health"
    RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)
    
    if [ "$RESPONSE" = "200" ]; then
        success "Health check passed"
    else
        error "Health check failed. HTTP status: $RESPONSE"
    fi
}

# Post-deployment tasks
post_deployment() {
    log "Running post-deployment tasks..."
    
    # Clear Next.js cache
    rm -rf .next/cache
    
    # Update file permissions
    chown -R www-data:www-data $APP_DIR
    chmod -R 755 $APP_DIR
    
    # Reload Nginx (if needed)
    if systemctl is-active --quiet nginx; then
        systemctl reload nginx
        log "Nginx reloaded"
    fi
    
    success "Post-deployment tasks completed"
}

# Rollback function
rollback() {
    error "Deployment failed. Starting rollback..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t $BACKUP_DIR/backup_*_app.tar.gz 2>/dev/null | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        log "Rolling back to: $LATEST_BACKUP"
        
        # Stop application
        pm2 stop $APP_NAME
        
        # Restore application files
        tar -xzf "$LATEST_BACKUP" -C "$APP_DIR"
        
        # Restart application
        pm2 start $APP_NAME
        
        warning "Rollback completed. Please check the application."
    else
        error "No backup found for rollback"
    fi
}

# Main deployment process
main() {
    log "🚀 Starting RokitBox deployment..."
    
    # Trap errors for rollback
    trap rollback ERR
    
    pre_deployment_checks
    create_backup
    deploy_application
    restart_services
    health_check
    post_deployment
    
    success "🎉 Deployment completed successfully!"
    log "Application is running at: https://rokitbox.cz"
}

# Run main function
main "$@"
