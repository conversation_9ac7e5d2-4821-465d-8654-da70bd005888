# Logo Setup - Robustní řešení

Tato dokumentace popisuje robustní implementaci loga v RokitBox aplikaci, k<PERSON><PERSON>, že se aplikace nikdy nerozbije kvůli problémům s logem.

## Implementované funkce

### 1. <PERSON>go komponenta (`src/components/Logo.tsx`)

**Hlavn<PERSON> funkce:**
- **Automatický fallback**: Pokud se PNG logo nenačte, automaticky se použije SVG verze
- **Retry mechanismus**: Až 2 pokusy o načtení PNG loga
- **Hydration safety**: Prevence hydration mismatch mezi serverem a klientem
- **Error handling**: Detailní logování chyb pro debugging
- **Cache busting**: Automatické přidání verze pro obnovení cache

**Použití:**
```tsx
// Základní použití
<Logo />

// S vlastními rozměry
<Logo width={300} height={90} />

// Bez SVG fallbacku (pouze text)
<Logo fallbackToSvg={false} />
```

### 2. <PERSON><PERSON><PERSON> (`src/components/ErrorBoundary.tsx`)

**Funkce:**
- Zachytává všechny React chyby v aplikaci
- Zobrazuje uživatelsky přívětivou chybovou stránku
- Umožňuje reset aplikace bez refreshe stránky
- Loguje technické detaily pro debugging

### 3. Next.js konfigurace (`next.config.js`)

**Optimalizace:**
- Povolení SVG obrázků
- Konfigurace Turbopack pro lepší performance
- Bezpečnostní nastavení pro obrázky
- Odstranění varování o workspace root

## Struktura souborů

```
public/
├── rokit-logo.png          # Hlavní PNG logo
└── rokit-logo.svg          # Záložní SVG logo (vestavěné v komponentě)

src/components/
├── Logo.tsx                # Robustní logo komponenta
└── ErrorBoundary.tsx       # Error boundary pro celou aplikaci

next.config.js              # Konfigurace Next.js
```

## Jak to funguje

### 1. Načítání loga
1. Komponenta se pokusí načíst PNG logo z `/rokit-logo.png`
2. Pokud se načtení nezdaří, automaticky se použije SVG fallback
3. Uživatel může kliknout na retry tlačítko (max 2x)
4. Pokud je fallback zakázán, zobrazí se pouze text "RokitBox"

### 2. Error handling
1. ErrorBoundary zachytí jakoukoliv chybu v React komponentách
2. Zobrazí se uživatelsky přívětivá chybová stránka
3. Uživatel může zkusit reset aplikace
4. Technické detaily jsou dostupné pro debugging

### 3. Performance optimalizace
- Logo se načítá s prioritou (`priority={true}`)
- Používá se `unoptimized` pro prevenci problémů s Next.js optimalizací
- Cache busting zajišťuje aktuální verzi při retry

## Výhody tohoto řešení

### ✅ Robustnost
- **Nikdy se nerozbije**: Vždy se zobrazí nějaká verze loga
- **Graceful degradation**: PNG → SVG → Text
- **Error recovery**: Možnost retry bez refreshe

### ✅ Uživatelská přívětivost
- **Rychlé načítání**: Priority loading pro logo
- **Vizuální konzistence**: SVG fallback vypadá stejně jako PNG
- **Jasné chybové hlášky**: Srozumitelné pro uživatele

### ✅ Developer experience
- **Detailní logování**: Console warnings pro debugging
- **Flexibilní API**: Konfigurovatelné props
- **TypeScript podpora**: Plná typová bezpečnost

### ✅ Maintenance
- **Jednoduchá aktualizace**: Stačí vyměnit PNG soubor
- **Centralizované řešení**: Jedna komponenta pro celou aplikaci
- **Dokumentované**: Jasné instrukce pro budoucí změny

## Řešení problémů

### Logo se nenačítá
1. Zkontrolujte, že `public/rokit-logo.png` existuje
2. Ověřte, že soubor není poškozený
3. Zkontrolujte console pro chybové hlášky
4. Zkuste refresh stránky

### Aplikace se rozbila
1. ErrorBoundary by měl zachytit chybu automaticky
2. Klikněte na "Zkusit znovu"
3. Pokud problém přetrvává, zkontrolujte console

### Performance problémy
1. Zkontrolujte velikost PNG souboru (doporučeno < 100KB)
2. Zvažte optimalizaci obrázku
3. Zkontrolujte network tab v dev tools

## Budoucí vylepšení

- [ ] Lazy loading pro non-critical použití
- [ ] WebP podpora s fallbackem
- [ ] Automatická optimalizace obrázků
- [ ] A/B testing různých verzí loga
- [ ] Metrics pro sledování úspěšnosti načítání

## NextAuth.js konfigurace

### Problém s výchozí login stránkou
Při prvním spuštění se zobrazovala výchozí NextAuth.js login stránka místo naší vlastní.

### Řešení
1. **Přidání pages konfigurace** do `src/lib/auth.ts`:
```typescript
pages: {
  signIn: '/login',
  error: '/login',
},
```

2. **Aktualizace middleware** v `src/middleware.ts`:
- Přesměrování z root URL přímo na `/login`
- Správné handling autentifikovaných uživatelů
- Prevence cyklických přesměrování

3. **Výsledek**:
- ✅ Vždy se zobrazuje naše vlastní login stránka
- ✅ Žádná výchozí NextAuth.js stránka
- ✅ Správné přesměrování z `/` na `/login`
- ✅ Autentifikovaní uživatelé jdou na `/dashboard`

## Závěr

Toto řešení zajišťuje maximální robustnost a uživatelskou přívětivost při práci s logem i autentifikací. Aplikace se nikdy nerozbije kvůli problémům s obrázky a vždy poskytne uživateli funkční rozhraní s konzistentním designem.
