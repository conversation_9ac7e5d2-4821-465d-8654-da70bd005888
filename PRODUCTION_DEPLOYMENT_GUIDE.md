# 🚀 Production Deployment Guide - RokitBox

Kompletní průvodce pro nasazení aplikace RokitBox do produkčního prostředí.

## 📋 Přehled změn pro produkci

### 🔧 Konfigurace prostředí
### 🗄️ Databáze setup
### 🌐 Domain & SSL
### 📧 Email konfigurace
### 🔒 Bezpečnost
### 📊 Monitoring & Logging

---

## 1. 🔧 Environment Variables (.env.production)

Vytvořte `.env.production` soubor s produkčními hodnotami:

```bash
# === DATABASE ===
DATABASE_URL="postgresql://username:password@localhost:5432/rokitbox_prod"

# === NEXTAUTH ===
NEXTAUTH_SECRET="super-secure-random-string-min-32-chars"
NEXTAUTH_URL="https://rokitbox.cz"

# === SMTP EMAIL ===
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM="RokitBox <<EMAIL>>"

# === PRODUCTION FLAGS ===
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://rokitbox.cz"

# === OPTIONAL: External Services ===
# SENTRY_DSN="https://your-sentry-dsn"
# ANALYTICS_ID="GA-XXXXXXXXX"
```

---

## 2. 🗄️ Databáze Setup

### PostgreSQL Production Setup

```bash
# 1. Nainstalovat PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# 2. Vytvořit databázi a uživatele
sudo -u postgres psql
CREATE DATABASE rokitbox_prod;
CREATE USER rokitbox_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE rokitbox_prod TO rokitbox_user;
\q

# 3. Spustit migrace
npx prisma migrate deploy
npx prisma generate

# 4. Vytvořit prvního superadmin uživatele
npx prisma db seed
```

### Database Backup Strategy

```bash
# Denní backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U rokitbox_user rokitbox_prod > /backups/rokitbox_$DATE.sql
find /backups -name "rokitbox_*.sql" -mtime +7 -delete
```

---

## 3. 🌐 Domain & SSL Setup

### DNS Konfigurace
```
A     @           YOUR_SERVER_IP
A     www         YOUR_SERVER_IP
CNAME mail        YOUR_SERVER_IP
```

### Nginx Konfigurace
```nginx
server {
    listen 80;
    server_name rokitbox.cz www.rokitbox.cz;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name rokitbox.cz www.rokitbox.cz;

    ssl_certificate /etc/letsencrypt/live/rokitbox.cz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/rokitbox.cz/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### SSL Certificate (Let's Encrypt)
```bash
# Nainstalovat Certbot
sudo apt install certbot python3-certbot-nginx

# Získat SSL certifikát
sudo certbot --nginx -d rokitbox.cz -d www.rokitbox.cz

# Auto-renewal
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 4. 📧 Email Konfigurace

### Gmail SMTP Setup
1. **Zapnout 2FA** na Gmail účtu
2. **Vytvořit App Password**:
   - Google Account → Security → 2-Step Verification → App passwords
   - Vybrat "Mail" a "Other (Custom name)"
   - Použít vygenerované heslo v `SMTP_PASSWORD`

### Vlastní email server (doporučeno)
```bash
# Postfix + Dovecot setup
sudo apt install postfix dovecot-core dovecot-imapd

# Konfigurace v /etc/postfix/main.cf
myhostname = mail.rokitbox.cz
mydomain = rokitbox.cz
myorigin = $mydomain
```

---

## 5. 🔒 Bezpečnost

### Firewall Setup
```bash
# UFW Firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3000  # Blokovat přímý přístup k Next.js
```

### Security Headers (Nginx)
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### Rate Limiting
```nginx
# V nginx.conf
http {
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}

# V server bloku
location /api/auth/ {
    limit_req zone=login burst=3 nodelay;
    proxy_pass http://localhost:3000;
}

location /api/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://localhost:3000;
}
```

---

## 6. 📊 Monitoring & Logging

### PM2 Process Manager
```bash
# Nainstalovat PM2
npm install -g pm2

# ecosystem.config.js
module.exports = {
  apps: [{
    name: 'rokitbox',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/rokitbox',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/rokitbox/error.log',
    out_file: '/var/log/rokitbox/out.log',
    log_file: '/var/log/rokitbox/combined.log'
  }]
}

# Spustit aplikaci
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Log Rotation
```bash
# /etc/logrotate.d/rokitbox
/var/log/rokitbox/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

---

## 7. 🚀 Deployment Script

### deploy.sh
```bash
#!/bin/bash
set -e

echo "🚀 Deploying RokitBox to production..."

# 1. Pull latest code
git pull origin main

# 2. Install dependencies
npm ci --only=production

# 3. Build application
npm run build

# 4. Run database migrations
npx prisma migrate deploy

# 5. Restart application
pm2 restart rokitbox

echo "✅ Deployment completed successfully!"
```

---

## 8. 📱 Health Checks

### Health Check Endpoint
Vytvořte `/api/health` endpoint:

```typescript
// src/app/api/health/route.ts
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Test database connection
    await prisma.$queryRaw`SELECT 1`
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    })
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      error: error.message
    }, { status: 503 })
  }
}
```

### Monitoring Script
```bash
#!/bin/bash
# health-check.sh
HEALTH_URL="https://rokitbox.cz/api/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -ne 200 ]; then
    echo "❌ Health check failed: $RESPONSE"
    # Restart application
    pm2 restart rokitbox
    # Send alert email
    echo "RokitBox health check failed" | mail -s "Alert: RokitBox Down" <EMAIL>
else
    echo "✅ Health check passed"
fi
```

---

## 9. 🔄 CI/CD Pipeline (GitHub Actions)

### .github/workflows/deploy.yml
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/rokitbox
          ./deploy.sh
```

---

## 10. 📋 Pre-deployment Checklist

### Před nasazením zkontrolujte:

- [ ] **Environment variables** jsou nastaveny
- [ ] **Database** je připravena a migrace proběhly
- [ ] **SSL certifikát** je nainstalován
- [ ] **Email SMTP** je funkční
- [ ] **Firewall** je nakonfigurován
- [ ] **Backup strategie** je nastavena
- [ ] **Monitoring** je aktivní
- [ ] **Health checks** fungují
- [ ] **Performance testing** proběhl
- [ ] **Security audit** byl proveden

### Post-deployment:

- [ ] **Smoke tests** prošly
- [ ] **Admin účet** je vytvořen
- [ ] **Email delivery** funguje
- [ ] **SSL** je aktivní
- [ ] **Monitoring** hlásí zdravý stav
- [ ] **Backup** byl otestován
- [ ] **Performance** je v pořádku

---

## 🆘 Troubleshooting

### Časté problémy:

1. **Database connection failed**
   - Zkontrolovat `DATABASE_URL`
   - Ověřit síťové připojení
   - Zkontrolovat PostgreSQL status

2. **Email sending failed**
   - Ověřit SMTP credentials
   - Zkontrolovat firewall (port 587)
   - Testovat s `telnet smtp.gmail.com 587`

3. **SSL certificate issues**
   - Obnovit certifikát: `sudo certbot renew`
   - Zkontrolovat DNS propagaci
   - Ověřit Nginx konfiguraci

4. **Application won't start**
   - Zkontrolovat PM2 logs: `pm2 logs`
   - Ověřit Node.js verzi
   - Zkontrolovat disk space

---

---

## 🚀 Rychlý Start - Deployment na server

### Krok 1: Příprava serveru

```bash
# 1. Připojte se na server
ssh root@your-server-ip

# 2. Spusťte setup script
wget https://raw.githubusercontent.com/your-repo/rokitbox/main/scripts/setup-production.sh
chmod +x setup-production.sh
./setup-production.sh
```

### Krok 2: Nasazení aplikace

```bash
# 1. Klonujte repository
cd /var/www
git clone https://github.com/your-repo/rokitbox.git
cd rokitbox

# 2. Nakonfigurujte environment
cp .env.production.template .env.production
nano .env.production  # Upravte podle potřeby

# 3. Spusťte deployment
chmod +x deploy.sh
./deploy.sh
```

### Krok 3: Vytvoření prvního uživatele

```bash
# Připojte se k databázi
psql -h localhost -U rokitbox_user -d rokitbox_prod

# Vytvořte superadmin uživatele
INSERT INTO "User" (id, email, password, role, "organizationId", "createdAt", "updatedAt")
VALUES (
  'cm0000000000000000000000',
  '<EMAIL>',
  '$2a$12$hashed_password_here',
  'SUPERADMIN',
  NULL,
  NOW(),
  NOW()
);
```

---

## 📁 Vytvořené soubory

### Konfigurační soubory:
- ✅ `ecosystem.config.js` - PM2 konfigurace
- ✅ `deploy.sh` - Deployment script
- ✅ `nginx.conf` - Nginx konfigurace
- ✅ `.github/workflows/deploy.yml` - CI/CD pipeline
- ✅ `scripts/setup-production.sh` - Server setup script

### API endpoints:
- ✅ `/api/health` - Health check endpoint

---

## 🔄 Deployment proces

### Automatický deployment (GitHub Actions):
1. **Push do main** branch
2. **Testy** se spustí automaticky
3. **Build** aplikace
4. **Deploy** na server
5. **Health check** ověří funkčnost

### Manuální deployment:
```bash
cd /var/www/rokitbox
./deploy.sh
```

---

## 📊 Monitoring

### Dostupné nástroje:
- **PM2 monitoring**: `pm2 monit`
- **Health check**: `https://rokitbox.cz/api/health`
- **Nginx logs**: `/var/log/nginx/rokitbox_*.log`
- **Application logs**: `/var/log/rokitbox/*.log`

### Automatické kontroly:
- ✅ **Health check** každých 5 minut
- ✅ **SSL renewal** automaticky
- ✅ **Log rotation** denně
- ✅ **Database backup** denně

---

## 🔒 Bezpečnostní opatření

### Implementováno:
- ✅ **SSL/TLS** encryption
- ✅ **Rate limiting** na API
- ✅ **Firewall** (UFW)
- ✅ **Fail2ban** proti brute force
- ✅ **Security headers**
- ✅ **CSRF protection**
- ✅ **SQL injection** protection (Prisma)

---

## 📞 Podpora

### V případě problémů:
1. **Zkontrolujte logy**: `pm2 logs rokitbox`
2. **Health check**: `curl https://rokitbox.cz/api/health`
3. **Restart aplikace**: `pm2 restart rokitbox`
4. **Rollback**: Použijte backup z `/var/backups/rokitbox/`

### Užitečné příkazy:
```bash
# Status aplikace
pm2 status

# Restart aplikace
pm2 restart rokitbox

# Zobrazit logy
pm2 logs rokitbox --lines 100

# Nginx status
systemctl status nginx

# Database backup
pg_dump -h localhost -U rokitbox_user rokitbox_prod > backup.sql

# SSL renewal
certbot renew --dry-run
```

---

## 🎯 Výsledek

Po dokončení deployment procesu budete mít:

### ✅ Plně funkční produkční aplikaci na `https://rokitbox.cz`
### ✅ Automatické SSL certifikáty s auto-renewal
### ✅ Zabezpečený server s firewall a rate limiting
### ✅ Monitoring a health checks
### ✅ Automatické backupy databáze
### ✅ CI/CD pipeline pro budoucí updates
### ✅ Kompletní logging a error handling

**Aplikace je připravena pro produkční provoz! 🚀**
