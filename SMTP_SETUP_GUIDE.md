# ✅ SMTP Setup Guide - Skutečné odesílání emailů FUNKČNÍ!

Gmail SMTP je úspěšně nakonfigurován a funkční! Emaily se skutečně odesílají.

## ✅ Aktuální stav - FUNKČNÍ!

**Gmail SMTP je nakonfigurován a funguje:**
- ✅ **SMTP Host**: smtp.gmail.com:587
- ✅ **Autentifikace**: jan.<PERSON><PERSON><PERSON><PERSON>@rokit.cz + App Password
- ✅ **Test úspěšný**: Email odeslán a doručen
- ✅ **Message ID**: Generuje se správně

**Aplikace nyní podporuje:**
- ✅ **Ukládání emailů** do souborů (debugging)
- ✅ **Skutečné odesílání** emailů přes Gmail SMTP
- ✅ **Email viewer** na `http://localhost:3000/dev/emails`
- ✅ **<PERSON><PERSON><PERSON><PERSON> režim**: Uklád<PERSON> + odesílá současně

## 📧 Možnosti SMTP služeb

### 1. Gmail SMTP (Doporučeno pro začátek)

#### Kroky nastavení:
1. **Zapněte 2FA** v Google účtu
2. **Vytvořte App Password**:
   - Jděte na https://myaccount.google.com/security
   - Klikněte na "2-Step Verification"
   - Klikněte na "App passwords"
   - Vyberte "Mail" a "Other"
   - Zadejte "RokitBox"
   - Zkopírujte vygenerované heslo

3. **Aktualizujte .env.local**:
```bash
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="vase-app-password"
SMTP_FROM="<EMAIL>"
```

### 2. Outlook/Hotmail SMTP

```bash
SMTP_HOST="smtp-mail.outlook.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="vase-heslo"
SMTP_FROM="<EMAIL>"
```

### 3. Mailtrap (Testování)

1. **Registrace**: https://mailtrap.io/register/signup
2. **Vytvořte inbox**
3. **Zkopírujte SMTP údaje**:

```bash
SMTP_HOST="live.smtp.mailtrap.io"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="api"
SMTP_PASSWORD="vase-mailtrap-heslo"
SMTP_FROM="<EMAIL>"
```

### 4. SendGrid (Profesionální)

1. **Registrace**: https://sendgrid.com/
2. **Vytvořte API klíč**
3. **Nastavení**:

```bash
SMTP_HOST="smtp.sendgrid.net"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="apikey"
SMTP_PASSWORD="vas-sendgrid-api-key"
SMTP_FROM="<EMAIL>"
```

## 🔧 Testování nastavení

### 1. Zkontrolujte konfiguraci
```bash
# Zkontrolujte .env.local
cat .env.local | grep SMTP
```

### 2. Restartujte aplikaci
```bash
npm run dev
```

### 3. Vytvořte testovacího uživatele
1. Jděte na `/users`
2. Klikněte "Přidat uživatele"
3. Zadejte svůj email
4. Zkontrolujte konzoli pro zprávy

### 4. Zkontrolujte výsledek
- **Konzole**: Uvidíte zprávy o odesílání
- **Email viewer**: `http://localhost:3000/dev/emails`
- **Vaše emailová schránka**: Skutečný email

## 📊 Monitoring

### Konzole zprávy:
```
📧 Email saved to file: Nastavení hesla - RokitBox (<EMAIL>)
📁 File: /path/to/.emails/email_123456789_abc123.json
🌐 View emails at: http://localhost:3000/dev/emails
📤 Email sent via SMTP: <message-id>
```

### Chybové zprávy:
```
SMTP sending failed: Invalid login
💾 Email only saved to file (no SMTP configured)
```

## 🛠️ Řešení problémů

### Gmail "Invalid login"
- Zkontrolujte, že máte zapnutou 2FA
- Použijte App Password, ne běžné heslo
- Zkontrolujte, že email a heslo jsou správné

### Outlook "Authentication failed"
- Zkontrolujte uživatelské jméno a heslo
- Možná potřebujete povolit "Less secure apps"

### Mailtrap nefunguje
- Zkontrolujte, že používáte správný inbox
- Ověřte SMTP údaje v Mailtrap dashboardu

### Obecné problémy
- Zkontrolujte internetové připojení
- Ověřte firewall nastavení
- Zkontrolujte port (587 vs 465)

## 🔒 Bezpečnost

### Doporučení:
1. **Nikdy necommitujte** .env.local do gitu
2. **Používejte App Passwords** místo hlavních hesel
3. **Rotujte hesla** pravidelně
4. **Monitorujte** odesílání emailů

### Produkční nasazení:
```bash
# Produkční .env
SMTP_HOST="smtp.sendgrid.net"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="apikey"
SMTP_PASSWORD="SG.xxx"
SMTP_FROM="<EMAIL>"
```

## 📈 Doporučené služby podle použití

### Development/Testing:
- **Mailtrap** - Nejlepší pro testování
- **Gmail** - Rychlé nastavení

### Staging:
- **SendGrid Free** - 100 emailů/den zdarma
- **Mailgun** - Dobrá alternativa

### Production:
- **SendGrid** - Spolehlivý, dobré API
- **Amazon SES** - Levný, škálovatelný
- **Mailgun** - Dobrý pro větší objemy

## 🎯 Aktuální stav

Po nastavení SMTP budete mít:
- ✅ **Ukládání emailů** do souborů (debugging)
- ✅ **Skutečné odesílání** emailů
- ✅ **Email viewer** pro kontrolu
- ✅ **Chybové zprávy** v konzoli
- ✅ **Fallback** na ukládání při chybě SMTP

Emaily se budou odesílat skutečně, ale zároveň se uloží do souborů pro debugging!
