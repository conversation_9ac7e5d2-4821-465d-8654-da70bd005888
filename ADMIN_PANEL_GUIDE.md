# Admin Panel Guide - Superadmin nástroje

Kompletní admin panel pro superadminy s přístupem k email catcheru a Prisma Studio.

## 🚀 Přístup k Admin Panelu

### URL: `http://localhost:3000/admin`

**Požadavky:**
- ✅ Přihlášení jako uživatel s rolí `SUPERADMIN`
- ✅ Automatické přesměrování na `/dashboard` pro ostatní role

### Výchozí superadmin účet:
```
Email: <EMAIL>
Heslo: admin123
Role: SUPERADMIN
```

## 📊 Funkce Admin Panelu

### 1. **Dashboard s statistikami**
- **Celkem uživatelů** - Počet všech uživatelů v systému
- **Organizace** - Počet organizací
- **Odeslané emaily** - Počet emailů v `.emails/` složce
- **Status systému** - Online/Offline indikátor

### 2. **Admin n<PERSON>**

#### 📧 Email Catcher
- **Popis**: Prohlížení všech odeslaných emailů
- **URL**: `/dev/emails`
- **Funkce**: 
  - Zobrazení všech emailů uložených v `.emails/` složce
  - Auto-refresh každých 5 sekund
  - Prohlížení HTML obsahu emailů
  - Mazání všech emailů

#### 🗄️ Prisma Studio
- **Popis**: Správa databáze a dat
- **Port**: `localhost:5555`
- **Funkce**:
  - ✅ **Spuštění/Zastavení** přímo z admin panelu
  - ✅ **Status monitoring** - zobrazuje, zda běží
  - ✅ **Automatické otevření** v novém tabu po spuštění
  - ✅ **Bezpečné zastavení** procesu

#### 👥 Správa uživatelů
- **Popis**: Administrace všech uživatelů
- **URL**: `/users`
- **Funkce**: Vytváření, editace, mazání uživatelů

#### 🏢 Správa organizací
- **Popis**: Administrace organizací
- **URL**: `/organizations`
- **Funkce**: Vytváření, editace, mazání organizací

### 3. **Nedávná aktivita**
- **Posledních 5 emailů** s časovými razítky
- **Příjemci a předměty** emailů
- **Datum a čas odeslání**

## 🔧 Technické detaily

### Middleware ochrana
```typescript
// Pouze SUPERADMIN má přístup
if (req.nextUrl.pathname.startsWith('/admin') || req.nextUrl.pathname.startsWith('/api/admin')) {
  return !!token && token.role === 'SUPERADMIN'
}
```

### API Endpointy

#### `/api/admin/stats` (GET)
```json
{
  "totalUsers": 5,
  "totalOrganizations": 3,
  "totalEmails": 12,
  "recentEmails": [...]
}
```

#### `/api/admin/prisma-studio` (POST)
```json
// Spuštění
{ "action": "start" }

// Zastavení  
{ "action": "stop" }

// Status
{ "action": "status" }
```

### Navigace
- **Admin Panel** odkaz se zobrazuje pouze pro superadminy
- **Ikona**: Shield (🛡️)
- **Pozice**: V hlavní navigaci pod "Uživatelé"

## 🎯 Použití

### 1. Přihlášení jako superadmin
1. Jděte na `http://localhost:3000/login`
2. Zadejte: `<EMAIL>` / `admin123`
3. V navigaci uvidíte "Admin Panel"

### 2. Spuštění Prisma Studio
1. Klikněte na "Admin Panel" v navigaci
2. Najděte "Prisma Studio" kartu
3. Klikněte "Spustit"
4. Automaticky se otevře `localhost:5555` v novém tabu

### 3. Prohlížení emailů
1. V admin panelu klikněte na "Email Catcher"
2. Nebo přímo na `http://localhost:3000/dev/emails`
3. Zobrazí se všechny odeslané emaily

### 4. Monitoring systému
- **Statistiky** se načítají automaticky při otevření
- **Status Prisma Studio** se kontroluje při načtení
- **Nedávné emaily** se zobrazují v chronologickém pořadí

## 🔒 Bezpečnost

### Omezení přístupu
- ✅ **Pouze SUPERADMIN** - Ostatní role jsou přesměrovány
- ✅ **Middleware ochrana** - Kontrola na úrovni Next.js
- ✅ **API ochrana** - Všechny admin API endpointy kontrolují roli
- ✅ **Development only** - Prisma Studio pouze v dev režimu

### Produkční nasazení
```typescript
// Prisma Studio se automaticky zakáže v produkci
if (process.env.NODE_ENV === 'production') {
  return NextResponse.json({ error: 'Not available in production' }, { status: 404 })
}
```

## 📱 Responsive design

Admin panel je plně responzivní:
- **Desktop**: 4 sloupce nástrojů
- **Tablet**: 2 sloupce nástrojů  
- **Mobile**: 1 sloupec nástrojů
- **Statistiky**: Adaptivní grid layout

## 🎨 UI/UX

### Barevné schéma
- **Email Catcher**: Modrá (`bg-blue-500`)
- **Prisma Studio**: Fialová (`bg-purple-500`)
- **Uživatelé**: Zelená (`bg-green-500`)
- **Organizace**: Oranžová (`bg-orange-500`)

### Interaktivní prvky
- **Hover efekty** na kartách nástrojů
- **Loading stavy** pro Prisma Studio
- **Status indikátory** (✅ Běží na localhost:5555)
- **Auto-refresh** pro statistiky

## 🚀 Budoucí vylepšení

- [ ] **Logs viewer** - Prohlížení aplikačních logů
- [ ] **System metrics** - CPU, RAM, disk usage
- [ ] **Database backup** - Záloha databáze z admin panelu
- [ ] **User activity** - Sledování aktivit uživatelů
- [ ] **Email templates** - Správa email šablon
- [ ] **Configuration** - Úprava nastavení aplikace

## 📋 Checklist pro superadminy

### Denní kontrola
- [ ] Zkontrolovat statistiky uživatelů
- [ ] Prohlédnout nedávné emaily
- [ ] Ověřit stav systému

### Týdenní údržba
- [ ] Vyčistit staré emaily z `.emails/` složky
- [ ] Zkontrolovat databázi přes Prisma Studio
- [ ] Ověřit organizace a uživatele

### Při problémech
- [ ] Zkontrolovat error logy v konzoli
- [ ] Restartovat Prisma Studio
- [ ] Ověřit SMTP konfiguraci
- [ ] Zkontrolovat databázové připojení

Admin panel poskytuje kompletní kontrolu nad systémem pro superadminy s intuitivním rozhraním a pokročilými nástroji pro správu.
