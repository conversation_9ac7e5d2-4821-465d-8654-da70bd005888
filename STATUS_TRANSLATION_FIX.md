# Status Translation Fix - České překlady statusů

Tato dokumentace popisuje opravu zobrazování statusů zařízení v češtině místo angličtiny.

## Problém

Status zařízení se zobrazoval v angličtině (např. "MAINTENANCE") místo v češtině (např. "Údržba").

## Příčina problému

Nesoulad mezi:
1. **Prisma schéma** - p<PERSON><PERSON><PERSON><PERSON><PERSON> enum `DeviceStatus` s hodnotami `ACTIVE`, `INACTIVE`, `MAINTENANCE`, `RETIRED` (velká písmena)
2. **React komponenta** - očekávala hodnoty `active`, `inactive`, `maintenance`, `retired` (malá písmena)

## Řešení

### 1. Oprava funkcí pro zobrazení statusu

#### `getStatusLabel()` funkce
```typescript
const getStatusLabel = (status: string) => {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
      return 'Aktivní'
    case 'INACTIVE':
      return 'Neaktivní'
    case 'MAINTENANCE':
      return 'Údržba'
    case 'RETIRED':
      return 'Vyřazeno'
    default:
      return status
  }
}
```

#### `getStatusColor()` funkce
```typescript
const getStatusColor = (status: string) => {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
      return 'bg-green-100 text-green-800'
    case 'INACTIVE':
      return 'bg-gray-100 text-gray-800'
    case 'MAINTENANCE':
      return 'bg-yellow-100 text-yellow-800'
    case 'RETIRED':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
```

### 2. Oprava select boxů

#### Filter select (pro filtrování zařízení)
```html
<option value="">Všechny stavy</option>
<option value="ACTIVE">Aktivní</option>
<option value="INACTIVE">Neaktivní</option>
<option value="MAINTENANCE">Údržba</option>
<option value="RETIRED">Vyřazeno</option>
```

#### Modal select (pro přidání/editaci zařízení)
```html
<option value="ACTIVE">Aktivní</option>
<option value="INACTIVE">Neaktivní</option>
<option value="MAINTENANCE">Údržba</option>
<option value="RETIRED">Vyřazeno</option>
```

### 3. Oprava TypeScript typů

```typescript
status: 'ACTIVE' as 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'RETIRED'
```

## Struktura dat

### Prisma Schema (`prisma/schema.prisma`)
```prisma
enum DeviceStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  RETIRED
}

model Device {
  // ...
  status DeviceStatus @default(ACTIVE)
  // ...
}
```

### Databáze
- Statusy se ukládají jako: `ACTIVE`, `INACTIVE`, `MAINTENANCE`, `RETIRED`

### Frontend zobrazení
- Statusy se zobrazují jako: `Aktivní`, `Neaktivní`, `Údržba`, `Vyřazeno`

## Mapování statusů

| Databáze | Zobrazení | Barva |
|----------|-----------|-------|
| `ACTIVE` | `Aktivní` | Zelená |
| `INACTIVE` | `Neaktivní` | Šedá |
| `MAINTENANCE` | `Údržba` | Žlutá |
| `RETIRED` | `Vyřazeno` | Červená |

## Výsledek

### ✅ Před opravou:
- Status se zobrazoval jako "MAINTENANCE"
- Filtrování nefungovalo správně
- Nekonzistentní hodnoty mezi UI a databází

### ✅ Po opravě:
- **Status se zobrazuje česky** - "Údržba" místo "MAINTENANCE"
- **Filtrování funguje** - správné porovnání hodnot
- **Konzistentní data** - UI používá stejné hodnoty jako databáze
- **Správné barvy** - žlutý badge pro údržbu

## Testování

Pro ověření funkčnosti:

1. **Zobrazení statusu**: Zkontrolujte, že se status zobrazuje česky
2. **Filtrování**: Otestujte filtrování podle statusu
3. **Přidání zařízení**: Ověřte, že se status správně ukládá
4. **Editace zařízení**: Zkontrolujte, že se status správně načítá a ukládá

## Budoucí rozšíření

Pokud budete přidávat nové statusy:

1. **Přidejte do Prisma schématu**:
```prisma
enum DeviceStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  RETIRED
  NEW_STATUS  // Nový status
}
```

2. **Aktualizujte funkce**:
```typescript
case 'NEW_STATUS':
  return 'Nový Status'
```

3. **Přidejte do select boxů**:
```html
<option value="NEW_STATUS">Nový Status</option>
```

4. **Spusťte migrace**:
```bash
npx prisma db push
```

Toto řešení zajišťuje konzistentní zobrazování statusů v češtině napříč celou aplikací.
