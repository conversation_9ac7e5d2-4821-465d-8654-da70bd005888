# 📦 RokitBox - Kompletní instalační p<PERSON>

**Verze:** 1.0  
**Datum:** Prosinec 2024  
**Repository:** https://github.com/jansloupensky/Rokit_Box  

---

## 📋 Obsah

1. [Požadavky na systém](#požadavky-na-systém)
2. [Příprava serveru](#příprava-serveru)
3. [Instalace aplikace](#instalace-aplikace)
4. [Konfigurace](#konfigurace)
5. [Spuštění](#spuštění)
6. [<PERSON>v<PERSON><PERSON><PERSON><PERSON> funk<PERSON>](#ověření-funkčnosti)
7. [Údržba a monitoring](#údržba-a-monitoring)
8. [Řešení problémů](#řešení-problémů)

---

## 🖥️ Požadavky na systém

### Minimální požadavky:
- **OS:** Ubuntu 20.04 LTS nebo novější
- **RAM:** 2 GB (doporučeno 4 GB)
- **Disk:** 20 GB volného místa
- **CPU:** 2 cores (doporučeno 4 cores)
- **Síť:** Veřejná IP adresa
- **Doména:** Registrovaná doména (např. rokitbox.cz)

### Software požadavky:
- Node.js 18+
- PostgreSQL 13+
- Nginx
- SSL certifikát (Let's Encrypt)

---

## 🚀 Krok 1: Příprava serveru

### 1.1 Připojení na server
```bash
ssh root@your-server-ip
```

### 1.2 Aktualizace systému
```bash
apt update && apt upgrade -y
```

### 1.3 Automatická instalace prostředí
```bash
# Stáhněte setup script
wget https://raw.githubusercontent.com/jansloupensky/Rokit_Box/main/scripts/setup-production.sh

# Nastavte oprávnění
chmod +x setup-production.sh

# Spusťte instalaci
./setup-production.sh
```

**Co script nainstaluje:**
- ✅ Node.js 18 a npm
- ✅ PostgreSQL databázi
- ✅ Nginx web server
- ✅ PM2 process manager
- ✅ SSL certifikát (Let's Encrypt)
- ✅ Firewall (UFW)
- ✅ Fail2ban bezpečnost
- ✅ Monitoring nástroje

### 1.4 Konfigurace DNS
Nastavte DNS záznamy pro vaši doménu:
```
A     @           YOUR_SERVER_IP
A     www         YOUR_SERVER_IP
CNAME mail        YOUR_SERVER_IP
```

---

## 📥 Krok 2: Instalace aplikace

### 2.1 Klonování repository
```bash
cd /var/www
git clone https://github.com/jansloupensky/Rokit_Box.git rokitbox
cd rokitbox
```

### 2.2 Instalace závislostí
```bash
npm ci --only=production
```

### 2.3 Generování Prisma klienta
```bash
npx prisma generate
```

---

## ⚙️ Krok 3: Konfigurace

### 3.1 Environment variables
```bash
# Zkopírujte template
cp .env.production.template .env.production

# Upravte konfiguraci
nano .env.production
```

### 3.2 Vyplňte následující hodnoty:
```bash
# === DATABASE ===
DATABASE_URL="postgresql://rokitbox_user:YOUR_DB_PASSWORD@localhost:5432/rokitbox_prod"

# === NEXTAUTH ===
NEXTAUTH_SECRET="your-super-secure-32-character-secret"
NEXTAUTH_URL="https://your-domain.com"

# === SMTP EMAIL ===
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-gmail-app-password"
SMTP_FROM="RokitBox <<EMAIL>>"

# === PRODUCTION ===
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://your-domain.com"
```

### 3.3 Generování NEXTAUTH_SECRET
```bash
openssl rand -base64 32
```

### 3.4 Gmail SMTP setup
1. Zapněte 2FA na Gmail účtu
2. Vygenerujte App Password:
   - Google Account → Security → 2-Step Verification → App passwords
   - Vyberte "Mail" a "Other (Custom name)"
   - Použijte vygenerované heslo v `SMTP_PASSWORD`

---

## 🗄️ Krok 4: Databáze

### 4.1 Spuštění migrací
```bash
npx prisma migrate deploy
```

### 4.2 Vytvoření prvního superadmin uživatele
```bash
npm run create-superadmin
```

Postupujte podle instrukcí a zadejte:
- Email adresu
- Heslo (min. 8 znaků)

---

## 🏗️ Krok 5: Build a spuštění

### 5.1 Build aplikace
```bash
npm run build
```

### 5.2 Spuštění s PM2
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 5.3 Konfigurace Nginx
```bash
# Zkopírujte konfiguraci
cp nginx.conf /etc/nginx/sites-available/your-domain.com

# Upravte doménu v souboru
sed -i 's/rokitbox.cz/your-domain.com/g' /etc/nginx/sites-available/your-domain.com

# Aktivujte site
ln -sf /etc/nginx/sites-available/your-domain.com /etc/nginx/sites-enabled/

# Testujte konfiguraci
nginx -t

# Restartujte Nginx
systemctl restart nginx
```

---

## ✅ Krok 6: Ověření funkčnosti

### 6.1 Health check
```bash
curl https://your-domain.com/api/health
```

Očekávaný výstup:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-05T10:30:00.000Z",
  "version": "1.0.0",
  "checks": {
    "database": {
      "status": "healthy",
      "responseTime": "15ms"
    }
  }
}
```

### 6.2 Test přihlášení
1. Otevřete `https://your-domain.com/login`
2. Přihlaste se s vytvořeným superadmin účtem
3. Ověřte přístup k admin panelu na `/admin`

### 6.3 Test email funkčnosti
1. Vytvořte nového uživatele v admin panelu
2. Zkontrolujte, že email byl odeslán
3. V development módu zkontrolujte `/dev/emails`

---

## 📊 Krok 7: Monitoring a údržba

### 7.1 Užitečné příkazy
```bash
# Status aplikace
pm2 status

# Zobrazení logů
pm2 logs rokitbox

# Restart aplikace
pm2 restart rokitbox

# Monitoring
pm2 monit

# Nginx status
systemctl status nginx

# Database backup
pg_dump -h localhost -U rokitbox_user rokitbox_prod > backup.sql
```

### 7.2 Automatické úlohy
Setup script automaticky nakonfiguruje:
- ✅ **Health check** každých 5 minut
- ✅ **SSL renewal** automaticky
- ✅ **Log rotation** denně
- ✅ **Database backup** denně

### 7.3 Log soubory
```bash
# Aplikační logy
tail -f /var/log/rokitbox/combined.log

# Nginx logy
tail -f /var/log/nginx/rokitbox_access.log
tail -f /var/log/nginx/rokitbox_error.log

# PM2 logy
pm2 logs rokitbox --lines 100
```

---

## 🔄 Krok 8: Deployment workflow

### 8.1 Automatický deployment (GitHub Actions)
Repository obsahuje GitHub Actions workflow pro automatické nasazení:

1. **Push** do `main` branch
2. **Automatické testy**
3. **Build** aplikace
4. **Deploy** na server
5. **Health check**

### 8.2 Manuální deployment
```bash
cd /var/www/rokitbox
./deploy.sh
```

Deploy script automaticky:
- ✅ Vytvoří backup
- ✅ Stáhne nejnovější kód
- ✅ Nainstaluje závislosti
- ✅ Spustí migrace
- ✅ Sestaví aplikace
- ✅ Restartuje služby
- ✅ Ověří funkčnost

---

## 🚨 Řešení problémů

### Aplikace se nespustí
```bash
# Zkontrolujte logy
pm2 logs rokitbox

# Zkontrolujte konfiguraci
cat .env.production

# Restartujte aplikaci
pm2 restart rokitbox
```

### Databáze nefunguje
```bash
# Zkontrolujte PostgreSQL
systemctl status postgresql

# Testujte připojení
psql -h localhost -U rokitbox_user -d rokitbox_prod

# Zkontrolujte migrace
npx prisma migrate status
```

### SSL problémy
```bash
# Obnovte certifikát
certbot renew

# Zkontrolujte konfiguraci
nginx -t

# Restartujte Nginx
systemctl restart nginx
```

### Email nefunguje
```bash
# Testujte SMTP připojení
telnet smtp.gmail.com 587

# Zkontrolujte logy
grep -i smtp /var/log/rokitbox/combined.log

# Ověřte Gmail App Password
```

### Vysoké využití paměti
```bash
# Zkontrolujte využití
pm2 monit

# Restartujte aplikaci
pm2 restart rokitbox

# Zkontrolujte systémové prostředky
htop
```

---

## 📞 Podpora

### Kontaktní informace
- **GitHub Issues:** https://github.com/jansloupensky/Rokit_Box/issues
- **Email:** <EMAIL>

### Užitečné odkazy
- **Repository:** https://github.com/jansloupensky/Rokit_Box
- **Documentation:** README.md v repository
- **Health Check:** https://your-domain.com/api/health

---

## 📝 Poznámky

### Bezpečnostní doporučení
- ✅ Pravidelně aktualizujte systém
- ✅ Monitorujte logy pro podezřelou aktivitu
- ✅ Zálohujte databázi pravidelně
- ✅ Používejte silná hesla
- ✅ Omezte SSH přístup

### Performance tipy
- ✅ Monitorujte využití prostředků
- ✅ Optimalizujte databázové dotazy
- ✅ Používejte CDN pro statické soubory
- ✅ Nastavte správné cache headers

---

**© 2024 RokitBox - Všechna práva vyhrazena**
