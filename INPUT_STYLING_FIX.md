# Input Styling Fix - <PERSON><PERSON><PERSON> p<PERSON> a černý text

Tato dokumentace popisuje opravu stylování všech input fieldů v aplikaci, aby měly b<PERSON><PERSON> pozadí a černý text při psaní.

## Problém

Všechny input fieldy (text, email, password, select, textarea) měly tmavé pozadí a špatně viditelný text, což způsobovalo špatnou uživatelskou zkušenost.

## Řešení

### 1. Globální CSS pravidla (`src/app/globals.css`)

Přidal jsem silná CSS pravidla s `!important`, kter<PERSON> zajišťují konzistentní styling:

```css
/* Fix input text color - Force white background and black text */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="date"],
textarea,
select {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="date"]:focus,
textarea:focus,
select:focus {
  color: #111827 !important;
  background-color: #ffffff !important;
  border-color: #ea580c !important;
  box-shadow: 0 0 0 1px #ea580c !important;
}

/* Placeholder text */
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
}

/* Disabled state */
input:disabled,
textarea:disabled,
select:disabled {
  background-color: #f9fafb !important;
  color: #6b7280 !important;
}
```

### 2. Komponenty aktualizace

Přidal jsem `text-gray-900 bg-white` CSS třídy do všech input fieldů v:

#### `src/app/devices/page.tsx`
- Název zařízení input
- Typ zařízení input  
- Model input
- Sériové číslo input
- Datum nákupu input (type="date")
- Konec záruky input (type="date")
- Stav select
- Umístění input
- Organizace select
- Poznámky textarea

#### `src/app/users/page.tsx`
- Email input
- Password input
- Role select
- Organizace select

#### `src/app/organizations/page.tsx`
- Název organizace input

#### `src/app/login/page.tsx`
- Email input (už měl správné styly)
- Password input (už měl správné styly)

### 3. Konzistentní CSS třídy

Všechny input fieldy nyní používají:
```css
className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm text-gray-900 bg-white"
```

## Výsledek

### ✅ Před opravou:
- Tmavé pozadí input fieldů
- Špatně viditelný text
- Nekonzistentní styling

### ✅ Po opravě:
- **Bílé pozadí** všech input fieldů
- **Černý text** při psaní
- **Šedý placeholder text** pro lepší UX
- **Oranžový focus ring** pro konzistenci s designem
- **Světle šedé pozadí** pro disabled fieldy
- **Konzistentní styling** napříč celou aplikací

## Technické detaily

### CSS Specificity
Použil jsem `!important` pravidla, protože Tailwind CSS má vysokou specificitu a běžná CSS pravidla by se neaplikovala.

### Dark Mode Override
Přidal jsem speciální pravidla pro dark mode, která zajišťují, že input fieldy zůstanou světlé i v tmavém režimu:

```css
@media (prefers-color-scheme: dark) {
  /* Same rules as light mode to override dark theme */
}
```

### Podporované typy
CSS pravidla pokrývají všechny používané typy inputů:
- `input[type="text"]`
- `input[type="email"]` 
- `input[type="password"]`
- `input[type="number"]`
- `input[type="search"]`
- `input[type="date"]`
- `textarea`
- `select`

## Testování

Aplikace byla otestována na všech stránkách:
- ✅ Login page - správné styling
- ✅ Devices page - všechny fieldy opraveny
- ✅ Users page - všechny fieldy opraveny  
- ✅ Organizations page - všechny fieldy opraveny
- ✅ Search a filter fieldy - správné styling

## Budoucí maintenance

Pro přidání nových input fieldů:
1. Použijte standardní Tailwind třídy
2. Přidejte `text-gray-900 bg-white` pro jistotu
3. Globální CSS pravidla zajistí konzistentní styling automaticky

Toto řešení zajišťuje, že všechny input fieldy budou mít konzistentní a uživatelsky přívětivý vzhled bez ohledu na to, kde se v aplikaci nacházejí.
