# 📄 Generování PDF dokumentace

Tento průvodce vysvětluje, jak vygenerovat PDF verzi instalační příručky RokitBox.

## 🚀 Rychlé generování PDF

### Metoda 1: <PERSON>k<PERSON> script (doporučeno)
```bash
# Nainstaluje puppeteer a vygeneruje PDF
npm run docs:build
```

### Metoda 2: Man<PERSON><PERSON><PERSON><PERSON> krok<PERSON>
```bash
# 1. Nainstalujte puppeteer
npm install puppeteer --save-dev

# 2. Vygenerujte PDF
npm run generate-pdf
```

## 📁 Výstupní soubory

Po úspěšném vygenerování najdete:
- **`RokitBox_Installation_Guide.pdf`** - Hlavní PDF dokument
- **`ROKITBOX_INSTALLATION_GUIDE.html`** - HTML verze (pro náhled)
- **`ROKITBOX_INSTALLATION_GUIDE.md`** - Markdown verze

## 🎨 Vlastnosti PDF

### Formátování:
- ✅ **Formát:** A4
- ✅ **Okraje:** 2cm ze všech stran
- ✅ **Fonty:** Segoe UI (profesionální vzhled)
- ✅ **Barvy:** RokitBox brand colors (#f97316)
- ✅ **Číslování stránek:** Automatické
- ✅ **Záhlaví/zápatí:** S názvem dokumentu

### Obsah:
- ✅ **Obsah:** Interaktivní odkazy
- ✅ **Kód bloky:** Syntax highlighting
- ✅ **Varování:** Barevně odlišené boxy
- ✅ **Checklists:** S emoji checkmarky
- ✅ **Tabulky:** Formátované pro tisk

## 🔧 Alternativní metody

### Online nástroje:
1. **Markdown to PDF:**
   - Otevřete `ROKITBOX_INSTALLATION_GUIDE.md`
   - Použijte online konvertor (např. markdown-pdf.com)

2. **HTML to PDF:**
   - Otevřete `ROKITBOX_INSTALLATION_GUIDE.html` v prohlížeči
   - Použijte Ctrl+P → "Uložit jako PDF"
   - Nastavte okraje na "Minimální"

3. **Pandoc (pokud máte nainstalovaný):**
   ```bash
   pandoc ROKITBOX_INSTALLATION_GUIDE.md -o RokitBox_Guide.pdf --pdf-engine=wkhtmltopdf
   ```

## 🐛 Řešení problémů

### Puppeteer se nenainstaluje:
```bash
# Na Ubuntu/Debian
sudo apt-get install -y gconf-service libasound2 libatk1.0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget

# Pak zkuste znovu
npm install puppeteer --save-dev
```

### PDF se nevygeneruje:
```bash
# Zkontrolujte, že HTML soubor existuje
ls -la ROKITBOX_INSTALLATION_GUIDE.html

# Spusťte s debug výstupem
DEBUG=puppeteer:* npm run generate-pdf
```

### Chybí fonty v PDF:
- PDF používá web-safe fonty
- Pokud se nezobrazují správně, zkuste otevřít HTML v prohlížeči a vytisknout jako PDF

## 📋 Checklist před distribucí

Před odesláním PDF zkontrolujte:
- [ ] Všechny odkazy fungují
- [ ] Kód bloky jsou čitelné
- [ ] Obrázky se zobrazují správně
- [ ] Číslování stránek je správné
- [ ] Obsah odpovídá aktuální verzi aplikace
- [ ] Kontaktní informace jsou aktuální

## 📞 Podpora

Pokud máte problémy s generováním PDF:
1. Zkontrolujte Node.js verzi: `node --version` (doporučeno 18+)
2. Zkuste alternativní metody výše
3. Kontaktujte: <EMAIL>

---

**Tip:** Pro nejlepší výsledky použijte automatický script `npm run docs:build`
