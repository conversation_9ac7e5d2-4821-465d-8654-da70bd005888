# Troubleshooting Guide - RokitBox

Tento průvodce vám pomůže vyřešit běžné problémy s aplikací RokitBox.

## 🚨 Aplikace se načítá š<PERSON>tně / Rozbitá navigace

### Problém: Role kontroly
**Symptomy:**
- Navigace se nezobrazuje správně
- "Načítám dashboard..." se zobrazuje nekonečně
- Chybí menu položky pro admin/superadmin

**Příčina:** Nekonzistentní role kontroly (lowercase vs uppercase)

**Řešení:**
```typescript
// ❌ ŠPATNĚ - lowercase
userProfile?.role === 'admin'
userProfile?.role === 'superadmin'

// ✅ SPRÁVNĚ - uppercase
userProfile?.role === 'ADMIN'
userProfile?.role === 'SUPERADMIN'
```

### Oprava:
1. **AuthContext** - Nekonvertovat role na lowercase
2. **Layout komponenta** - Používat uppercase role
3. **Všechny stránky** - Konzistentní role kontroly

## 🔐 Problémy s přihlášením

### NextAuth JWT chyby
**Chyba:** `Invalid Compact JWE`

**Řešení:**
```bash
# Vyčistit Next.js cache
rm -rf .next

# Restartovat server
npm run dev
```

### Session problémy
**Symptomy:**
- Uživatel se nemůže přihlásit
- Session se neukládá

**Kontrola:**
1. Zkontrolovat `.env.local`:
```bash
NEXTAUTH_SECRET="your-secret-here"
NEXTAUTH_URL="http://localhost:3000"
```

2. Zkontrolovat databázové připojení:
```bash
npx prisma studio
```

## 📧 Email problémy

### SMTP nefunguje
**Symptomy:**
- Emaily se neposílají
- "SMTP sending failed" v konzoli

**Kontrola konfigurace:**
```bash
# .env.local
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM="<EMAIL>"
```

**Test SMTP:**
```bash
node -e "
const nodemailer = require('nodemailer');
const transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  auth: { user: 'your-email', pass: 'your-app-password' }
});
transporter.verify().then(console.log).catch(console.error);
"
```

### Email Catcher nefunguje
**Symptomy:**
- "Něco se pokazilo" na `/dev/emails`
- JavaScript chyby v konzoli

**Řešení:**
1. Zkontrolovat API endpoint:
```bash
curl http://localhost:3000/api/dev/emails
```

2. Zkontrolovat `.emails/` složku:
```bash
ls -la .emails/
```

## 🗄️ Databáze problémy

### Prisma Studio se nespustí
**Chyba:** Port 5555 již používán

**Řešení:**
```bash
# Najít proces na portu 5555
lsof -i :5555

# Ukončit proces
kill -9 <PID>

# Spustit znovu
npx prisma studio
```

### Migrace problémy
**Chyba:** Database schema out of sync

**Řešení:**
```bash
# Reset databáze (POZOR: Smaže data!)
npx prisma migrate reset

# Nebo push schema
npx prisma db push
```

## 🔧 Obecné problémy

### Server se nespustí
**Kontrola:**
1. **Port 3000 obsazen:**
```bash
lsof -i :3000
kill -9 <PID>
```

2. **Node modules:**
```bash
rm -rf node_modules package-lock.json
npm install
```

3. **Next.js cache:**
```bash
rm -rf .next
npm run dev
```

### TypeScript chyby
**Řešení:**
```bash
# Restart TypeScript serveru
# V VS Code: Ctrl+Shift+P -> "TypeScript: Restart TS Server"

# Nebo rebuild
npm run build
```

## 🐛 Debug režim

### Zapnutí debug logů
V `src/contexts/AuthContext.tsx` je automatické logování:
```typescript
// Konzole výstup
AuthContext Debug: {
  status: 'loading',
  loading: true,
  hasSession: false,
  hasUser: false,
  userRole: undefined,
  userEmail: undefined
}
```

### Browser Developer Tools
1. **Otevřít:** F12
2. **Console tab:** JavaScript chyby
3. **Network tab:** API requesty
4. **Application tab:** Local Storage, Cookies

## 📋 Checklist při problémech

### Základní kontrola:
- [ ] Server běží na portu 3000
- [ ] Databáze je dostupná
- [ ] `.env.local` je správně nakonfigurován
- [ ] Next.js cache je vyčištěn

### Role problémy:
- [ ] Všechny role kontroly používají uppercase
- [ ] AuthContext nekonvertuje role na lowercase
- [ ] Layout komponenta má správné role kontroly

### Email problémy:
- [ ] SMTP konfigurace je správná
- [ ] Gmail App Password je platný
- [ ] `.emails/` složka existuje
- [ ] Email Catcher API funguje

### Admin Panel:
- [ ] Uživatel má roli SUPERADMIN
- [ ] Middleware povoluje přístup k /admin
- [ ] Prisma Studio se může spustit

## 🚀 Restart sekvence

Při vážných problémech:
```bash
# 1. Zastavit server
Ctrl+C

# 2. Vyčistit cache
rm -rf .next
rm -rf node_modules/.cache

# 3. Restartovat databázi (pokud potřeba)
npx prisma db push

# 4. Spustit server
npm run dev

# 5. Zkontrolovat konzoli pro chyby
```

## 📞 Kdy kontaktovat podporu

Kontaktujte vývojáře, pokud:
- Problém přetrvává po restart sekvenci
- Databáze je poškozená
- SMTP konfigurace nefunguje ani s jinými službami
- Aplikace se vůbec nespustí

**Připravte tyto informace:**
- Chybové zprávy z konzole
- Obsah `.env.local` (bez hesel!)
- Verze Node.js: `node --version`
- Operační systém
- Kroky k reprodukci problému
